// 测试字段选择功能
const testFieldMapping = {
  field_1: {
    name: '姓名',
    type: 'string',
    required: true,
    enabled: true,
    description: '用户姓名'
  },
  field_2: {
    name: '性别',
    type: 'string',
    required: false,
    enabled: false, // 未启用
    description: '用户性别'
  },
  field_3: {
    name: '电话',
    type: 'string',
    required: true,
    enabled: true,
    description: '联系电话'
  },
  field_4: {
    name: '备注',
    type: 'text',
    required: false,
    enabled: false, // 未启用
    description: '备注信息'
  }
}

// 测试启用字段过滤
function getEnabledFields(fieldMapping) {
  return Object.entries(fieldMapping)
    .filter(([key, config]) => config.enabled !== false)
    .reduce((acc, [key, config]) => {
      acc[key] = config
      return acc
    }, {})
}

// 测试字段数量计算
function getEnabledFieldCount(fieldMapping) {
  return Object.values(fieldMapping).filter(
    field => field.enabled !== false
  ).length
}

console.log('原始字段映射:', testFieldMapping)
console.log('启用的字段:', getEnabledFields(testFieldMapping))
console.log('启用字段数量:', getEnabledFieldCount(testFieldMapping))

// 预期结果：
// - 启用的字段应该只有 field_1 和 field_3
// - 启用字段数量应该是 2
