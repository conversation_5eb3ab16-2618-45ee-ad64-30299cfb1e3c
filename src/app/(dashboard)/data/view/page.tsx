'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Form,
  Modal,
  message,
  Popconfirm,
  Tag,
  Drawer,
  Descriptions,
  Alert,
  Row,
  Col,
} from 'antd'
import { MobileTable } from '@/components/ui'
import {
  SearchOutlined,
  ReloadOutlined,
  FilterOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  ExportOutlined,
  ClearOutlined,
  DownloadOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface FormConfig {
  formId: string
  formName: string
  fieldMapping: Record<string, any>
}

interface DataRecord {
  id: string
  serial_number: number
  created_at: string
  [key: string]: any
}

export default function DataViewPage() {
  const searchParams = useSearchParams()
  const [forms, setForms] = useState<FormConfig[]>([])
  const [selectedForm, setSelectedForm] = useState<string>('')
  const [currentFormConfig, setCurrentFormConfig] = useState<FormConfig | null>(
    null
  )
  const [data, setData] = useState<DataRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [searchForm] = Form.useForm()
  const [filterVisible, setFilterVisible] = useState(false)
  const [detailVisible, setDetailVisible] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<DataRecord | null>(null)
  const [editVisible, setEditVisible] = useState(false)
  const [editingRecord, setEditingRecord] = useState<DataRecord | null>(null)
  const [editForm] = Form.useForm()
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  // 获取表单列表
  const fetchForms = async () => {
    try {
      const response = await fetch('/api/forms')
      const result = await response.json()
      if (result.success) {
        setForms(result.data.forms)

        // 检查URL参数中的formId
        const urlFormId = searchParams.get('formId')
        if (urlFormId) {
          setSelectedForm(urlFormId)
        } else if (result.data.forms.length > 0 && !selectedForm) {
          setSelectedForm(result.data.forms[0].formId)
        }
      }
    } catch (error) {
      console.error('获取表单列表失败:', error)
    }
  }

  // 获取数据
  const fetchData = async (formId: string, searchParams = {}, page = 1) => {
    if (!formId) return

    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.pageSize.toString(),
        ...searchParams,
      })

      const response = await fetch(`/api/data/${formId}?${params}`)
      const result = await response.json()

      if (result.success) {
        setData(result.data.records)
        setPagination({
          current: result.data.pagination.page,
          pageSize: result.data.pagination.limit,
          total: result.data.pagination.total,
        })

        // 存储当前表单配置
        if (result.data.formConfig) {
          setCurrentFormConfig(result.data.formConfig)
        }
      } else {
        message.error('获取数据失败')
      }
    } catch (error) {
      console.error('获取数据失败:', error)
      message.error('获取数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 搜索处理
  const handleSearch = (values: any) => {
    const searchParams: Record<string, string> = {}

    Object.entries(values).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length === 2) {
          // 日期范围
          searchParams[`${key}_start`] = dayjs(value[0]).format('YYYY-MM-DD')
          searchParams[`${key}_end`] = dayjs(value[1]).format('YYYY-MM-DD')
        } else {
          searchParams[key] = String(value)
        }
      }
    })

    fetchData(selectedForm, searchParams, 1)
    setFilterVisible(false)
  }

  // 清空搜索
  const handleClearSearch = () => {
    searchForm.resetFields()
    fetchData(selectedForm, {}, 1)
    setFilterVisible(false)
  }

  // 表单切换
  const handleFormChange = (formId: string) => {
    setSelectedForm(formId)
    setSelectedRowKeys([])
    searchForm.resetFields()
    fetchData(formId, {}, 1)
  }

  // 查看详情
  const showDetail = (record: DataRecord) => {
    setSelectedRecord(record)
    setDetailVisible(true)
  }

  // 编辑记录
  const showEdit = (record: DataRecord) => {
    setEditingRecord(record)
    setEditVisible(true)

    // 填充编辑表单（只处理启用的字段）
    const formData: any = { serial_number: record.serial_number }
    if (currentFormConfig) {
      Object.entries(currentFormConfig.fieldMapping)
        .filter(([_, config]: [string, any]) => config.enabled !== false) // 只处理启用的字段
        .forEach(([key, _]) => {
          formData[key] = record[key]
        })
    }
    editForm.setFieldsValue(formData)
  }

  // 保存编辑
  const handleEdit = async (values: any) => {
    if (!editingRecord) return

    try {
      const response = await fetch(
        `/api/data/${selectedForm}?id=${editingRecord.id}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            updateData: values,
          }),
        }
      )

      const result = await response.json()

      if (result.success) {
        message.success('编辑成功')
        setEditVisible(false)
        editForm.resetFields()
        fetchData(selectedForm, searchForm.getFieldsValue(), pagination.current)
      } else {
        message.error(result.error || '编辑失败')
      }
    } catch (error) {
      console.error('编辑失败:', error)
      message.error('编辑失败')
    }
  }

  // 删除记录
  const handleDelete = async (record: DataRecord) => {
    try {
      const response = await fetch(
        `/api/data/${selectedForm}?id=${record.id}`,
        {
          method: 'DELETE',
        }
      )

      const result = await response.json()

      if (result.success) {
        message.success('删除成功')
        fetchData(selectedForm, searchForm.getFieldsValue(), pagination.current)
      } else {
        message.error(result.error || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的数据')
      return
    }

    try {
      const response = await fetch(`/api/data/${selectedForm}/batch`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ids: selectedRowKeys,
        }),
      })

      const result = await response.json()

      if (result.success) {
        message.success(`成功删除 ${selectedRowKeys.length} 条数据`)
        setSelectedRowKeys([])
        fetchData(selectedForm, {}, pagination.current)
      } else {
        message.error(result.error || '删除失败')
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      message.error('删除失败')
    }
  }

  // 导出数据
  const handleExport = async (format: 'excel' | 'csv') => {
    if (!selectedForm) {
      message.error('请先选择表单')
      return
    }

    // 检查浏览器兼容性
    if (!window.URL || !window.URL.createObjectURL) {
      message.error('您的浏览器不支持文件下载功能，请升级浏览器')
      return
    }

    const hide = message.loading('正在导出数据，请稍候...', 0)
    
    try {
      const searchParams = searchForm.getFieldsValue()
      const params = new URLSearchParams()

      Object.entries(searchParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value) && value.length === 2) {
            params.append(`${key}_start`, dayjs(value[0]).format('YYYY-MM-DD'))
            params.append(`${key}_end`, dayjs(value[1]).format('YYYY-MM-DD'))
          } else {
            params.append(key, String(value))
          }
        }
      })

      params.append('format', format)
      if (selectedRowKeys.length > 0) {
        params.append('ids', selectedRowKeys.join(','))
      }

      const response = await fetch(`/api/data/${selectedForm}/export?${params}`)

      if (!response.ok) {
        // 简化错误处理，避免读取可能包含中文的响应体
        let errorMessage = `导出失败: HTTP ${response.status}`
        
        // 只处理常见的HTTP状态码
        switch (response.status) {
          case 401:
            errorMessage = '未授权访问，请重新登录'
            break
          case 404:
            errorMessage = '表单配置不存在或已禁用'
            break
          case 400:
            errorMessage = '请求参数错误'
            break
          case 500:
            errorMessage = '服务器内部错误，请稍后重试'
            break
          default:
            errorMessage = `导出失败: ${response.statusText || '未知错误'}`
        }
        
        throw new Error(errorMessage)
      }

      // 获取响应内容
      const arrayBuffer = await response.arrayBuffer()
      
      // 检查返回的数据是否为空
      if (arrayBuffer.byteLength === 0) {
        throw new Error('导出的文件为空')
      }

      // 创建 Blob 对象，明确指定正确的 MIME 类型
      const mimeType = format === 'excel' 
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'text/csv;charset=utf-8'
      
      const blob = new Blob([arrayBuffer], { type: mimeType })

      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${selectedForm}_data_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.${format === 'excel' ? 'xlsx' : 'csv'}`
      
      // 添加到DOM并触发下载
      document.body.appendChild(a)
      a.click()
      
      // 清理资源
      setTimeout(() => {
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }, 100)
      
      hide()
      message.success(`${format === 'excel' ? 'Excel' : 'CSV'}文件导出成功`)
      
    } catch (error) {
      hide()
      console.error('导出失败:', error)
      
      let errorMessage = '导出失败'
      if (error instanceof Error) {
        errorMessage = error.message
      } else if (typeof error === 'string') {
        errorMessage = error
      }
      
      message.error(errorMessage)
    }
  }

  // 生成表格列
  const generateColumns = (): ColumnsType<DataRecord> => {
    if (!selectedForm || !currentFormConfig) return []

    const columns: ColumnsType<DataRecord> = [
      {
        title: '序号',
        dataIndex: 'serial_number',
        width: 80,
        sorter: true,
      },
    ]

    // 动态字段列（只显示启用的字段）
    Object.entries(currentFormConfig.fieldMapping)
      .filter(([_, config]: [string, any]) => config.enabled !== false) // 只包含启用的字段
      .forEach(([key, config]: [string, any]) => {
        columns.push({
          title: config.name,
          dataIndex: key,
          ellipsis: true,
          render: value => {
            if (value === null || value === undefined) return '-'
            if (typeof value === 'object') {
              return <Tag>{JSON.stringify(value)}</Tag>
            }
            return String(value)
          },
        })
      })

    columns.push(
      {
        title: '创建时间',
        dataIndex: 'created_at',
        width: 160,
        sorter: true,
        render: date => dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '操作',
        key: 'actions',
        width: 120,
        fixed: 'right',
        render: (_, record) => (
          <Space size="small">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => showDetail(record)}
            />
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => showEdit(record)}
            />
            <Popconfirm
              title="确认删除"
              description="确定要删除这条数据吗？"
              onConfirm={() => handleDelete(record)}
              okText="确认"
              cancelText="取消"
            >
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Space>
        ),
      }
    )

    return columns
  }

  // 生成编辑表单
  const generateEditForm = () => {
    if (!selectedForm || !currentFormConfig) return null

    return (
      <Form form={editForm} layout="vertical" onFinish={handleEdit}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Form.Item
              label="序号"
              name="serial_number"
              rules={[{ required: true, message: '请输入序号' }]}
            >
              <Input placeholder="请输入序号" type="number" />
            </Form.Item>
          </Col>

          {Object.entries(currentFormConfig.fieldMapping)
            .filter(([_, config]: [string, any]) => config.enabled !== false) // 只显示启用的字段
            .map(([key, config]: [string, any]) => (
              <Col xs={24} sm={12} key={key}>
                <Form.Item
                  label={config.name}
                  name={key}
                  rules={
                    config.required
                      ? [{ required: true, message: `请输入${config.name}` }]
                      : []
                  }
                >
                  {config.type === 'date' ? (
                    <DatePicker style={{ width: '100%' }} />
                  ) : config.type === 'number' ? (
                    <Input type="number" placeholder={`请输入${config.name}`} />
                  ) : config.type === 'select' && config.options ? (
                    <Select placeholder={`请选择${config.name}`}>
                      {config.options.map((option: any) => (
                        <Option key={option.value} value={option.value}>
                          {option.label}
                        </Option>
                      ))}
                    </Select>
                  ) : (
                    <Input placeholder={`请输入${config.name}`} />
                  )}
                </Form.Item>
              </Col>
            )
          )}
        </Row>
      </Form>
    )
  }

  // 生成搜索表单
  const generateSearchForm = () => {
    if (!selectedForm || !currentFormConfig) return null

    return (
      <Form form={searchForm} layout="vertical" onFinish={handleSearch}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item label="序号" name="serial_number">
              <Input placeholder="请输入序号" />
            </Form.Item>
          </Col>

          {Object.entries(currentFormConfig.fieldMapping)
            .filter(([_, config]: [string, any]) => config.enabled !== false) // 只显示启用的字段
            .slice(0, 5)
            .map(([key, config]: [string, any]) => (
              <Col xs={24} sm={12} md={8} lg={6} key={key}>
                <Form.Item label={config.name} name={key}>
                  {config.type === 'date' ? (
                    <RangePicker style={{ width: '100%' }} />
                  ) : config.type === 'number' ? (
                    <Input type="number" placeholder={`请输入${config.name}`} />
                  ) : (
                    <Input placeholder={`请输入${config.name}`} />
                  )}
                </Form.Item>
              </Col>
            ))}

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item label="创建时间" name="created_at">
              <RangePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col span={24}>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
              >
                搜索
              </Button>
              <Button onClick={handleClearSearch} icon={<ClearOutlined />}>
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    )
  }

  useEffect(() => {
    fetchForms()
  }, [])

  useEffect(() => {
    if (selectedForm) {
      fetchData(selectedForm)
    }
  }, [selectedForm])

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  }

  return (
    <div className="space-y-4">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <Title level={2} className="mb-1">
            数据查看
          </Title>
          <Text type="secondary">
{"field_1": {"name": "姓名", "type": "string", "enabled": true, "required": false, "description": ""}, "field_2": {"name": "性别", "type": "string", "enabled": false, "required": false, "description": ""}, "field_3": {"name": "电话", "type": "string", "enabled": false, "required": false, "description": ""}, "field_4": {"name": "选项", "type": "string", "enabled": false, "required": false, "description": ""}, "field_5": {"name": "邮箱", "type": "string", "enabled": false, "required": false, "description": ""}, "field_6": {"name": "年龄", "type": "number", "enabled": false, "required": false, "description": ""}, "field_7": {"name": "地址", "type": "string", "enabled": false, "required": false, "description": ""}, "field_8": {"name": "字段8", "type": "array", "enabled": false, "required": false, "description": ""}, "field_10": {"name": "字段10", "type": "string", "enabled": false, "required": false, "description": ""}, "field_11": {"name": "字段11", "type": "string", "enabled": false, "required": false, "description": ""}, "field_12": {"name": "字段12", "type": "string", "enabled": false, "required": false, "description": ""}, "field_13": {"name": "字段13", "type": "string", "enabled": false, "required": false, "description": ""}, "field_14": {"name": "字段14", "type": "string", "enabled": false, "required": false, "description": ""}, "field_15": {"name": "字段15", "type": "string", "enabled": false, "required": false, "description": ""}, "field_16": {"name": "字段16", "type": "string", "enabled": false, "required": false, "description": ""}, "field_17": {"name": "字段17", "type": "string", "enabled": false, "required": false, "description": ""}, "field_18": {"name": "字段18", "type": "string", "enabled": false, "required": false, "description": ""}, "field_19": {"name": "字段19", "type": "string", "enabled": false, "required": false, "description": ""}, "field_21": {"name": "字段21", "type": "string", "enabled": false, "required": false, "description": ""}, "field_22": {"name": "字段22", "type": "string", "enabled": false, "required": false, "description": ""}, "field_23": {"name": "字段23", "type": "string", "enabled": false, "required": false, "description": ""}, "field_24": {"name": "字段24", "type": "string", "enabled": false, "required": false, "description": ""}, "field_25": {"name": "字段25", "type": "string", "enabled": false, "required": false, "description": ""}, "x_field_1": {"name": "备注", "type": "text", "enabled": false, "required": false, "description": ""}, "share_link": {"name": "share_link", "type": "string", "enabled": false, "required": false, "description": ""}, "field_6_associated_field_1": {"name": "字段6_associated_field_1", "type": "string", "enabled": false, "required": false, "description": ""}, "field_6_associated_field_2": {"name": "字段6_associated_field_2", "type": "string", "enabled": false, "required": false, "description": ""}, "field_6_associated_field_3": {"name": "字段6_associated_field_3", "type": "string", "enabled": false, "required": false, "description": ""}, "field_12_associated_field_1": {"name": "字段12_associated_field_1", "type": "string", "enabled": false, "required": false, "description": ""}, "field_13_associated_field_1": {"name": "字段13_associated_field_1", "type": "string", "enabled": false, "required": false, "description": ""}, "field_12_associated_field_24": {"name": "字段12_associated_field_24", "type": "string", "enabled": false, "required": false, "description": ""}, "field_13_associated_field_24": {"name": "字段13_associated_field_24", "type": "string", "enabled": false, "required": false, "description": ""}, "x_field_customer_qQ7IIQZX_associated_name": {"name": "x_字段customer_qQ7IIQZX_associated_name", "type": "text", "enabled": false, "required": false, "description": ""}, "x_field_customer_qQ7IIQZX_associated_email": {"name": "x_字段customer_qQ7IIQZX_associated_email", "type": "string", "enabled": false, "required": false, "description": ""}, "x_field_customer_qQ7IIQZX_associated_mobile": {"name": "x_字段customer_qQ7IIQZX_associated_mobile", "type": "text", "enabled": false, "required": false, "description": ""}, "x_field_customer_qQ7IIQZX_associated_x_field_weixin_nickname": {"name": "x_字段customer_qQ7IIQZX_associated_扩展字段weixin_nickname", "type": "text", "enabled": false, "required": false, "description": ""}, "x_field_customer_qQ7IIQZX_associated_x_field_weixin_headimgurl": {"name": "x_字段customer_qQ7IIQZX_associated_扩展字段weixin_headimgurl", "type": "text", "enabled": false, "required": false, "description": ""}}
          </Text>
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() =>
              fetchData(
                selectedForm,
                searchForm.getFieldsValue(),
                pagination.current
              )
            }
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 表单选择和操作 */}
      <Card size="small">
        <div className="flex justify-between items-center">
          <Space>
            <Text strong>选择表单:</Text>
            <Select
              value={selectedForm}
              onChange={handleFormChange}
              style={{ width: 200 }}
              placeholder="请选择表单"
            >
              {forms.map(form => (
                <Option key={form.formId} value={form.formId}>
                  {form.formName}
                </Option>
              ))}
            </Select>
            <Button
              icon={<FilterOutlined />}
              onClick={() => setFilterVisible(true)}
            >
              高级搜索
            </Button>
          </Space>

          <Space>
            {selectedRowKeys.length > 0 && (
              <>
                <Text type="secondary">已选择 {selectedRowKeys.length} 条</Text>
                <Popconfirm
                  title="批量删除"
                  description={`确定要删除选中的 ${selectedRowKeys.length} 条数据吗？`}
                  onConfirm={handleBatchDelete}
                  okText="确认删除"
                  cancelText="取消"
                  okType="danger"
                >
                  <Button danger icon={<DeleteOutlined />}>
                    批量删除
                  </Button>
                </Popconfirm>
              </>
            )}
            <Button
              icon={<ExportOutlined />}
              onClick={() => handleExport('excel')}
            >
              导出Excel
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => handleExport('csv')}
            >
              导出CSV
            </Button>
          </Space>
        </div>
      </Card>

      {/* 数据表格 */}
      <Card className="mobile-card">
        {selectedForm ? (
          <MobileTable
            rowSelection={rowSelection}
            columns={generateColumns()}
            dataSource={Array.isArray(data) ? data : []}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                if (pageSize !== pagination.pageSize) {
                  setPagination({ ...pagination, pageSize })
                }
                fetchData(selectedForm, searchForm.getFieldsValue(), page)
              },
              responsive: true,
              showLessItems: true,
            }}
            size="small"
            showScrollHint={true}
          />
        ) : (
          <div className="text-center py-8">
            <Text type="secondary">请先选择要查看的表单</Text>
          </div>
        )}
      </Card>

      {/* 高级搜索抽屉 */}
      <Drawer
        title="高级搜索"
        placement="right"
        width={600}
        open={filterVisible}
        onClose={() => setFilterVisible(false)}
        extra={
          <Space>
            <Button onClick={() => setFilterVisible(false)}>取消</Button>
          </Space>
        }
      >
        <Alert
          message="搜索提示"
          description="支持按字段精确搜索，日期字段支持范围搜索。搜索条件之间为AND关系。"
          type="info"
          showIcon
          className="mb-4"
        />
        {generateSearchForm()}
      </Drawer>

      {/* 详情模态框 */}
      <Modal
        title="数据详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {selectedRecord && (
          <div className="space-y-4">
            <Descriptions bordered size="small" column={2}>
              <Descriptions.Item label="序号" span={1}>
                {selectedRecord.serial_number}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间" span={1}>
                {dayjs(selectedRecord.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>

              {selectedForm &&
                currentFormConfig &&
                Object.entries(currentFormConfig.fieldMapping)
                  .filter(([_, config]: [string, any]) => config.enabled !== false) // 只显示启用的字段
                  .map(([key, config]: [string, any]) => (
                    <Descriptions.Item label={config.name} span={2} key={key}>
                      {selectedRecord[key] === null ||
                      selectedRecord[key] === undefined
                        ? '-'
                        : typeof selectedRecord[key] === 'object'
                          ? JSON.stringify(selectedRecord[key], null, 2)
                          : String(selectedRecord[key])}
                    </Descriptions.Item>
                  )
                )}
            </Descriptions>

            {selectedRecord.raw_data && (
              <div>
                <Title level={5}>原始数据</Title>
                <pre className="bg-gray-50 p-3 rounded text-xs overflow-auto">
                  {JSON.stringify(selectedRecord.raw_data, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 编辑模态框 */}
      <Modal
        title="编辑数据"
        open={editVisible}
        onCancel={() => {
          setEditVisible(false)
          editForm.resetFields()
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setEditVisible(false)
              editForm.resetFields()
            }}
            className="touch-target"
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => editForm.submit()}
            className="touch-target"
          >
            保存
          </Button>,
        ]}
        width={800}
        className="mobile-modal"
      >
        {generateEditForm()}
      </Modal>
    </div>
  )
}
